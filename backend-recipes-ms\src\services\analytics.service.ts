import Analytics, {
  AnalyticsEventType,
  AnalyticsEntityType,
} from "../models/Analytics";
import { Op, QueryTypes } from "sequelize";
import { sequelize } from "../models/index";
import settingsService from "./settings.service";

interface AnalyticsQuery {
  eventType?: AnalyticsEventType;
  entityId?: number;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

interface DashboardStats {
  // Core Business Metrics
  totalRecipes: number;
  topCategory: {
    name: string;
    count: number;
  };
  highestImpressionRecipe: {
    name: string;
    impressions: number;
  };

  // Recipe Analytics
  totalViews: number;
  totalContactSubmissions: number;
  totalBookmarks: number; // NEW

  // Dashboard Charts Data
  recipeViewsTrend: any[]; // Top 10 recipes with views for last 30 days
  categoryPerformance: any[]; // Bar chart data
  userEngagementHeatmap: any[]; // Heatmap data for user engagement
  conversionFunnel: any[]; // Conversion analytics data
  recentActivity: any[];
}

class AnalyticsService {
  /**
   * Track any event with flexible metadata - Supports organization filtering
   */
  async trackEvent(data: {
    eventType: AnalyticsEventType;
    entityType: AnalyticsEntityType;
    entityId?: number;
    organizationId?: string;
    userId?: number;
    ipAddress?: string;
    userAgent?: string;
    metadata?: any;
  }): Promise<Analytics> {
    // Remove referrer from metadata if present (for public APIs)
    const cleanMetadata = { ...data.metadata };
    delete cleanMetadata.referrer;

    return await Analytics.trackEvent({
      event_type: data.eventType,
      entity_type: data.entityType,
      entity_id: data.entityId,
      organization_id: data.organizationId,
      user_id: data.userId,
      ip_address: data.ipAddress,
      user_agent: data.userAgent,
      metadata: cleanMetadata,
    });
  }

  // REMOVED: getAnalytics() - Unused generic function
  // Use specific analytics functions instead: getCtaClickAnalytics, getContactSubmissionAnalytics, etc.

  /**
   * Get dashboard statistics - Organization-based and optimized
   */
  async getDashboardStats(
    organizationId: string | null | undefined,
    dateRange: string = "last_30_days"
  ): Promise<DashboardStats> {
    try {
      const { startDate, endDate } = await this.getDateRange(
        dateRange,
        organizationId
      );

      // Get all counts in parallel with organization-filtered queries
      const [
        recipeCount,
        topCategoryData,
        totalViews,
        contactCount,
        bookmarkCount,
        recipeViewsTrend,
        categoryPerformance,
        userEngagementHeatmap,
        conversionFunnel,
        recentActivity,
        highestImpressionRecipe,
      ] = await Promise.all([
        this.getRecipeCount(organizationId),
        this.getTopCategory(organizationId, startDate, endDate),
        this.getTotalRecipeViews(organizationId, startDate, endDate),
        this.getContactSubmissions(organizationId, startDate, endDate),
        this.getTotalBookmarks(organizationId),
        this.getRecipeViewsTrend(organizationId, startDate, endDate),
        this.getCategoryPerformance(organizationId, startDate, endDate),
        this.getUserEngagementHeatmap(organizationId, startDate, endDate),
        this.getConversionFunnel(organizationId, startDate, endDate),
        this.getRecentActivity(organizationId, 10),
        this.getHighestImpressionRecipe(organizationId),
      ]);

      return {
        // Core Business Metrics
        totalRecipes: recipeCount,
        topCategory: topCategoryData,
        highestImpressionRecipe: highestImpressionRecipe,

        // Recipe Analytics
        totalViews: totalViews,
        totalContactSubmissions: contactCount,
        totalBookmarks: bookmarkCount,

        // Dashboard Charts Data
        recipeViewsTrend: recipeViewsTrend,
        categoryPerformance: categoryPerformance,
        userEngagementHeatmap: userEngagementHeatmap,
        conversionFunnel: conversionFunnel,
        recentActivity: recentActivity,
      } as DashboardStats;
    } catch (error) {
      // Log error for debugging but return safe fallback data
      // console.error("Dashboard stats error:", error);
      return {
        totalRecipes: 0,
        topCategory: { name: "No Data", count: 0 },
        highestImpressionRecipe: { name: "No Data", impressions: 0 },
        totalViews: 0,
        totalContactSubmissions: 0,
        totalBookmarks: 0,
        recipeViewsTrend: [],
        categoryPerformance: [],
        userEngagementHeatmap: [],
        conversionFunnel: [],
        recentActivity: [],
      } as DashboardStats;
    }
  }

  /** Get total active bookmarks */
  private async getTotalBookmarks(
    organizationId: string | null | undefined
  ): Promise<number> {
    try {
      // Filter on the recipe's organisation to ensure historical rows without organisation_id are counted correctly
      const orgFilter = organizationId
        ? `AND r.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT COUNT(*) as total
         FROM mo_recipe_bookmarks rb
         JOIN mo_recipe r ON r.id = rb.recipe_id AND r.recipe_status != 'deleted'
         WHERE rb.status = 'active' ${orgFilter}`,
        {
          replacements: { organizationId },
          type: QueryTypes.SELECT,
        }
      );
      return (result[0] as any)?.total || 0;
    } catch {
      return 0;
    }
  }

  // ============================================================================
  // PRIVATE HELPER METHODS - Organization-filtered and optimized
  // ============================================================================

  /**
   * Get total recipe count - Organization filtered
   */
  private async getRecipeCount(
    organizationId?: string | null
  ): Promise<number> {
    try {
      // Apply strict organization filtering
      if (organizationId !== null && organizationId !== undefined) {
        const result = await sequelize.query(
          `SELECT COUNT(*) as total FROM mo_recipe
           WHERE recipe_status != 'deleted' AND organization_id = :organizationId`,
          {
            replacements: { organizationId },
            type: QueryTypes.SELECT,
          }
        );
        return (result[0] as any)?.total || 0;
      } else {
        // If organizationId is null/undefined (admin with no filter), show all data
        const result = await sequelize.query(
          `SELECT COUNT(*) as total FROM mo_recipe
           WHERE recipe_status != 'deleted'`,
          {
            type: QueryTypes.SELECT,
          }
        );
        return (result[0] as any)?.total || 0;
      }
    } catch {
      return 0;
    }
  }

  /**
   * Get top performing category based on recipe views - Organization filtered
   */
  private async getTopCategory(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<{ name: string; count: number }> {
    try {
      const orgFilter = organizationId
        ? `AND r.organization_id = :organizationId`
        : "";

      const dateFilter = `AND r.updated_at BETWEEN :startDate AND :endDate`;

      const result = await sequelize.query(
        `SELECT c.category_name, COUNT(DISTINCT r.id) AS count
         FROM mo_category c
         JOIN mo_recipe_category rc ON rc.category_id = c.id AND rc.status = 'active'
         JOIN mo_recipe r ON r.id = rc.recipe_id AND r.recipe_status != 'deleted'
         WHERE 1=1 ${orgFilter} ${dateFilter}
         GROUP BY c.category_name
         ORDER BY count DESC
         LIMIT 1`,
        {
          replacements: { organizationId, startDate, endDate },
          type: QueryTypes.SELECT,
        }
      );

      if (result.length > 0) {
        const topCategory = result[0] as any;
        return {
          name: topCategory.category_name || "Unknown",
          count: parseInt(topCategory.count) || 0,
        };
      }

      return { name: "No Data", count: 0 };
    } catch {
      return { name: "No Data", count: 0 };
    }
  }

  /**
   * Get total recipe views from analytics - Organization filtered and only from published recipes
   */
  private async getTotalRecipeViews(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    try {
      // Use raw query to join with recipe table and filter by published status
      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT COUNT(*) as total
         FROM mo_recipe_analytics a
         JOIN mo_recipe r ON a.entity_id = r.id
         WHERE a.event_type = 'recipe_view'
         AND a.created_at BETWEEN :startDate AND :endDate
         AND r.recipe_status = 'publish'
         AND r.recipe_status != 'deleted'
         ${orgFilter}`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );
      return (result[0] as any)?.total || 0;
    } catch {
      return 0;
    }
  }

  /**
   * Get contact form submissions count - Organization filtered
   */
  private async getContactSubmissions(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<number> {
    try {
      const whereCondition: any = {
        event_type: AnalyticsEventType.CONTACT_FORM_SUBMIT,
        created_at: {
          [Op.between]: [startDate, endDate],
        },
      };

      // Apply strict organization filtering
      if (organizationId !== null && organizationId !== undefined) {
        whereCondition.organization_id = organizationId;
      }
      // If organizationId is undefined (admin with no filter), show all data

      const result = await Analytics.count({
        where: whereCondition,
      });
      return result || 0;
    } catch {
      return 0;
    }
  }

  /**
   * Highest-impression recipe based on analytics table (same logic as recipe views trend)
   */
  private async getHighestImpressionRecipe(
    organizationId?: string | null
  ): Promise<{
    name: string;
    impressions: number;
  }> {
    try {
      // Use same filtering logic as recipe views trend
      const recipeOrgFilter = organizationId
        ? `AND r.organization_id = :organizationId`
        : "";

      const analyticsOrgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           r.id as recipe_id,
           r.recipe_title,
           COUNT(a.id) as impressions
         FROM mo_recipe r
         LEFT JOIN mo_recipe_analytics a ON r.id = a.entity_id
           AND a.event_type = 'recipe_view'
           ${analyticsOrgFilter}
         WHERE r.recipe_status = 'publish' ${recipeOrgFilter}
         GROUP BY r.id, r.recipe_title
         ORDER BY impressions DESC
         LIMIT 1`,
        {
          replacements: { organizationId },
          type: QueryTypes.SELECT,
        }
      );

      if (result.length > 0) {
        const row = result[0] as any;
        return {
          name: this.cleanRecipeName(row.recipe_title) || "Unknown Recipe",
          impressions: parseInt(row.impressions) || 0,
        };
      }

      return { name: "No Data", impressions: 0 };
    } catch {
      return { name: "No Data", impressions: 0 };
    }
  }

  /**
   * Get user engagement heatmap data - Organization filtered dynamic query
   */
  private async getUserEngagementHeatmap(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    try {
      // Use strict organization filtering logic
      const orgFilter = organizationId
        ? `AND organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           HOUR(a.created_at) as hour,
           DAYOFWEEK(a.created_at) as day_of_week,
           COUNT(*) as activity_count
         FROM mo_recipe_analytics a
         JOIN mo_recipe r ON a.entity_id = r.id
         WHERE a.created_at BETWEEN :startDate AND :endDate
         AND r.recipe_status = 'publish'
         ${orgFilter.replace("organization_id", "a.organization_id")}
         GROUP BY HOUR(a.created_at), DAYOFWEEK(a.created_at)
         ORDER BY day_of_week, hour`,
        {
          replacements: {
            startDate,
            endDate,
            organizationId: organizationId || null,
          },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => ({
        hour: parseInt(row.hour) || 0,
        day_of_week: parseInt(row.day_of_week) || 0,
        activity_count: parseInt(row.activity_count) || 0,
      }));
    } catch {
      // Return empty array on error to prevent dashboard crashes
      return [];
    }
  }

  /**
   * Get conversion funnel data - Organization filtered dynamic query
   */
  private async getConversionFunnel(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    try {
      // Use strict organization filtering logic
      const orgFilter = organizationId
        ? `AND organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           'Recipe Views' as stage,
           1 as stage_order,
           COUNT(CASE WHEN a.event_type = 'recipe_view' THEN 1 END) as stage_count,
           COUNT(DISTINCT a.user_id) as unique_users
         FROM mo_recipe_analytics a
         JOIN mo_recipe r ON a.entity_id = r.id
         WHERE a.created_at BETWEEN :startDate AND :endDate
         AND r.recipe_status = 'publish'
         ${orgFilter.replace("organization_id", "a.organization_id")}

         UNION ALL

         SELECT
           'CTA Clicks' as stage,
           2 as stage_order,
           COUNT(CASE WHEN a.event_type = 'cta_click' THEN 1 END) as stage_count,
           COUNT(DISTINCT a.user_id) as unique_users
         FROM mo_recipe_analytics a
         JOIN mo_recipe r ON a.entity_id = r.id
         WHERE a.created_at BETWEEN :startDate AND :endDate
         AND r.recipe_status = 'publish'
         ${orgFilter.replace("organization_id", "a.organization_id")}

         UNION ALL

         SELECT
           'Contact Forms' as stage,
           3 as stage_order,
           COUNT(CASE WHEN a.event_type = 'contact_form_submit' THEN 1 END) as stage_count,
           COUNT(DISTINCT a.user_id) as unique_users
         FROM mo_recipe_analytics a
         JOIN mo_recipe r ON a.entity_id = r.id
         WHERE a.created_at BETWEEN :startDate AND :endDate
         AND r.recipe_status = 'publish'
         ${orgFilter.replace("organization_id", "a.organization_id")}

         ORDER BY stage_order`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      // Calculate conversion rates
      const totalViews =
        result.find((r: any) => r.stage === "Recipe Views")?.stage_count || 1;

      return result.map((row: any) => {
        const stageCount = parseInt(row.stage_count) || 0;
        const conversionRate =
          totalViews > 0 ? (stageCount / totalViews) * 100 : 0;

        return {
          stage: row.stage,
          count: stageCount,
          conversion_rate: Math.round(conversionRate * 100) / 100,
        };
      });
    } catch {
      // Return default funnel structure on error
      return [
        {
          stage: "Recipe Views",
          stage_order: 1,
          count: 0,
          unique_users: 0,
          conversion_rate: 0,
          drop_off_rate: 100,
        },
        {
          stage: "CTA Clicks",
          stage_order: 2,
          count: 0,
          unique_users: 0,
          conversion_rate: 0,
          drop_off_rate: 100,
        },
        {
          stage: "Contact Forms",
          stage_order: 3,
          count: 0,
          unique_users: 0,
          conversion_rate: 0,
          drop_off_rate: 100,
        },
      ];
    }
  }

  /**
   * Get recipe views trend - Organization filtered, Top 10 recipes with single recipe name
   */
  private async getRecipeViewsTrend(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    try {
      // For recipe views trend, we need to filter both recipe table and analytics table
      const recipeOrgFilter = organizationId
        ? `AND r.organization_id = :organizationId`
        : "";

      const analyticsOrgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           r.id as recipe_id,
           r.recipe_title,
           COUNT(a.id) as views
         FROM mo_recipe r
         LEFT JOIN mo_recipe_analytics a ON r.id = a.entity_id
           AND a.event_type = 'recipe_view'
           AND a.created_at BETWEEN :startDate AND :endDate
           ${analyticsOrgFilter}
         WHERE r.recipe_status = 'publish' ${recipeOrgFilter}
         GROUP BY r.id, r.recipe_title
         ORDER BY views ASC
         LIMIT 10`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => ({
        recipe_id: parseInt(row.recipe_id),
        recipe_name:
          this.cleanRecipeName(row.recipe_title) || `Recipe ${row.recipe_id}`,
        views: parseInt(row.views) || 0,
      }));
    } catch {
      // Return empty array on error to prevent dashboard crashes
      return [];
    }
  }

  /**
   * Get category performance data - Organization filtered
   */
  private async getCategoryPerformance(
    organizationId: string | null | undefined,
    startDate: Date,
    endDate: Date
  ): Promise<any[]> {
    try {
      // Filter both recipe and analytics tables for organization
      const recipeOrgFilter = organizationId
        ? `AND r.organization_id = :organizationId`
        : "";

      const analyticsOrgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           c.category_name,
           COUNT(DISTINCT r.id) as recipe_count,
           COUNT(a.id) as views
         FROM mo_category c
         LEFT JOIN mo_recipe_category rc ON c.id = rc.category_id AND rc.status = 'active'
         LEFT JOIN mo_recipe r ON rc.recipe_id = r.id
         LEFT JOIN mo_recipe_analytics a ON r.id = a.entity_id
           AND a.event_type = 'recipe_view'
           AND a.created_at BETWEEN :startDate AND :endDate
           ${analyticsOrgFilter}
         WHERE c.category_status = 'active'
           AND c.category_type = 'recipe'
           AND (r.id IS NULL OR r.recipe_status = 'publish')
           ${recipeOrgFilter ? recipeOrgFilter.replace("r.organization_id", "r.organization_id") : ""}
         GROUP BY c.id, c.category_name
         HAVING COUNT(DISTINCT r.id) > 0
         ORDER BY views DESC
         LIMIT 10`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => ({
        category_name: row.category_name || "Unknown",
        recipe_count: parseInt(row.recipe_count) || 0,
        views: parseInt(row.views) || 0,
      }));
    } catch {
      // Return empty array on error to prevent dashboard crashes
      return [];
    }
  }

  /**
   * Get recent activity data - Organization filtered
   */
  private async getRecentActivity(
    organizationId: string | null | undefined,
    limit: number = 10
  ): Promise<any[]> {
    try {
      // Use strict organization filtering - only show data for this organization
      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           a.event_type,
           a.entity_id as recipe_id,
           a.created_at,
           a.user_id,
           r.recipe_title,
           COALESCE(r.recipe_title, CONCAT('Recipe ', a.entity_id)) as actual_recipe_name,
           CASE 
             WHEN a.user_id IS NOT NULL THEN 
               CONCAT(COALESCE(u.user_first_name, ''), ' ', COALESCE(u.user_last_name, ''))
             ELSE NULL 
           END as user_full_name
         FROM mo_recipe_analytics a
         LEFT JOIN mo_recipe r ON a.entity_id = r.id
         LEFT JOIN nv_users u ON a.user_id = u.id AND u.user_status NOT IN ('cancelled', 'deleted')
         WHERE a.entity_type = 'recipe'
         AND (r.id IS NULL OR r.recipe_status = 'publish') ${orgFilter}
         ORDER BY a.created_at DESC
         LIMIT :limit`,
        {
          replacements: { organizationId, limit },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => {
        const recipeName =
          this.cleanRecipeName(row.actual_recipe_name) ||
          `Recipe ${row.recipe_id}`;
        const userName = row.user_full_name ? row.user_full_name.trim() : null;

        return {
          event_type: row.event_type,
          recipe_id: parseInt(row.recipe_id),
          recipe_name: recipeName,
          created_at: row.created_at,
          user_id: row.user_id,
          user_name: userName || null,
          activity_description: this.generateUserFriendlyActivity(
            row.event_type,
            recipeName,
            userName,
            false // isCurrentUser - would need to be passed from context
          ),
          // Keep the old format for backward compatibility
          legacy_description: this.getActivityDescription(
            row.event_type,
            recipeName
          ),
        };
      });
    } catch {
      // Return empty array on error to prevent dashboard crashes
      return [];
    }
  }

  /**
   * Get recent activity data with enhanced context - Organization filtered
   * This method provides enhanced context including user information, current user detection,
   * and multiple message format variations
   */
  async getRecentActivityWithContext(
    organizationId: string | null | undefined,
    currentUserId?: number | null,
    limit: number = 10
  ): Promise<any[]> {
    try {
      // Use strict organization filtering - only show data for this organization
      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      const result = await sequelize.query(
        `SELECT
           a.event_type,
           a.entity_id as recipe_id,
           a.created_at,
           a.user_id,
           r.recipe_title,
           COALESCE(r.recipe_title, CONCAT('Recipe ', a.entity_id)) as actual_recipe_name,
           CASE 
             WHEN a.user_id IS NOT NULL THEN 
               CONCAT(COALESCE(u.user_first_name, ''), ' ', COALESCE(u.user_last_name, ''))
             ELSE NULL 
           END as user_full_name,
           u.user_email,
           CASE WHEN a.user_id = :currentUserId THEN 1 ELSE 0 END as is_current_user,
           CASE WHEN a.user_id IS NULL THEN 1 ELSE 0 END as is_anonymous
         FROM mo_recipe_analytics a
         LEFT JOIN mo_recipe r ON a.entity_id = r.id
         LEFT JOIN nv_users u ON a.user_id = u.id AND u.user_status NOT IN ('cancelled', 'deleted')
         WHERE a.entity_type = 'recipe'
         AND (r.id IS NULL OR r.recipe_status = 'publish') ${orgFilter}
         ORDER BY a.created_at DESC
         LIMIT :limit`,
        {
          replacements: { organizationId, limit, currentUserId },
          type: QueryTypes.SELECT,
        }
      );

      return result.map((row: any) => {
        const recipeName =
          this.cleanRecipeName(row.actual_recipe_name) ||
          `Recipe ${row.recipe_id}`;
        const userName = row.user_full_name ? row.user_full_name.trim() : null;
        const isCurrentUser = Boolean(row.is_current_user);
        const isAnonymous = Boolean(row.is_anonymous);

        // Generate different message variations
        const formalMessage = this.generateUserFriendlyActivity(
          row.event_type,
          recipeName,
          userName,
          isCurrentUser
        );

        const casualMessage = this.generateCasualActivityMessage(
          row.event_type,
          recipeName,
          userName,
          isCurrentUser
        );

        const shortMessage = this.generateShortActivityMessage(
          row.event_type,
          recipeName,
          isCurrentUser
        );

        return {
          event_type: row.event_type,
          recipe_id: parseInt(row.recipe_id),
          recipe_name: recipeName,
          created_at: row.created_at,
          user_id: row.user_id,
          user_name: userName || null,
          user_email: row.user_email || null,
          is_current_user: isCurrentUser,
          is_anonymous: isAnonymous,
          activity_description: formalMessage,
          activity_variations: {
            formal: formalMessage,
            casual: casualMessage,
            short: shortMessage,
          },
          // Keep the old format for backward compatibility
          legacy_description: this.getActivityDescription(
            row.event_type,
            recipeName
          ),
        };
      });
    } catch (error) {
      console.error("Error fetching recent activity with context:", error);
      // Return empty array on error to prevent dashboard crashes
      return [];
    }
  }

  /**
   * Generate casual activity message variations
   */
  private generateCasualActivityMessage(
    eventType: string,
    recipeName: string,
    userName?: string | null,
    isCurrentUser: boolean = false
  ): string {
    const isAnonymous = !userName || userName.trim() === "";

    // Determine the user display text for casual tone
    let userDisplay: string;
    if (isCurrentUser) {
      userDisplay = "You";
    } else if (isAnonymous) {
      userDisplay = "Someone";
    } else {
      userDisplay = userName;
    }

    switch (eventType) {
      case "recipe_view":
        if (isCurrentUser) {
          return `You checked out ${recipeName}`;
        } else if (isAnonymous) {
          return `Someone browsed ${recipeName}`;
        } else {
          return `${userDisplay} browsed ${recipeName}`;
        }
      case "cta_click":
        if (isCurrentUser) {
          return `You clicked on ${recipeName}`;
        } else if (isAnonymous) {
          return `Someone clicked on ${recipeName}`;
        } else {
          return `${userDisplay} clicked on ${recipeName}`;
        }
      case "contact_form_submit":
        if (isCurrentUser) {
          return `You reached out about ${recipeName}`;
        } else if (isAnonymous) {
          return `Someone contacted about ${recipeName}`;
        } else {
          return `${userDisplay} reached out about ${recipeName}`;
        }
      default:
        return `${userDisplay} interacted with ${recipeName}`;
    }
  }

  /**
   * Generate short activity message variations
   */
  private generateShortActivityMessage(
    eventType: string,
    recipeName: string,
    isCurrentUser: boolean = false
  ): string {
    const prefix = isCurrentUser ? "You" : "";

    switch (eventType) {
      case "recipe_view":
        return isCurrentUser
          ? `You viewed ${recipeName}`
          : `Viewed ${recipeName}`;
      case "cta_click":
        return isCurrentUser
          ? `You clicked ${recipeName}`
          : `Clicked ${recipeName}`;
      case "contact_form_submit":
        return isCurrentUser
          ? `You contacted ${recipeName}`
          : `Contact ${recipeName}`;
      default:
        return isCurrentUser ? `You: ${recipeName}` : recipeName;
    }
  }

  /**
   * Helper method to clean recipe names (remove "recipe_title" or "recipe title" prefix if present)
   */
  private cleanRecipeName(
    recipeName: string | null | undefined
  ): string | null {
    if (!recipeName) return null;

    let cleaned = recipeName.trim();

    // Remove "recipe_title " prefix if it exists
    if (cleaned.startsWith("recipe_title ")) {
      cleaned = cleaned.substring("recipe_title ".length).trim();
    }

    // Remove "recipe title " prefix if it exists
    if (cleaned.startsWith("recipe title ")) {
      cleaned = cleaned.substring("recipe title ".length).trim();
    }

    return cleaned;
  }

  /**
   * Helper method to generate activity descriptions
   */
  private getActivityDescription(
    eventType: string,
    recipeName: string
  ): string {
    switch (eventType) {
      case "recipe_view":
        return `Viewed "${recipeName}"`;
      case "cta_click":
        return `Clicked CTA on "${recipeName}"`;
      case "contact_form_submit":
        return `Submitted contact form for "${recipeName}"`;
      default:
        return `Activity on "${recipeName}"`;
    }
  }

  /**
   * Enhanced helper method to generate user-friendly activity descriptions
   * @param eventType - Type of event (recipe_view, cta_click, etc.)
   * @param recipeName - Name of the recipe
   * @param userName - Full name of the user (null for anonymous)
   * @param isCurrentUser - Whether this is the current user viewing the activity
   * @returns User-friendly activity message
   */
  public generateUserFriendlyActivity(
    eventType: string,
    recipeName: string,
    userName?: string | null,
    isCurrentUser: boolean = false
  ): string {
    const isAnonymous = !userName || userName.trim() === "";

    // Determine the user display text
    let userDisplay: string;
    if (isCurrentUser) {
      userDisplay = "you";
    } else if (isAnonymous) {
      userDisplay = "a visitor";
    } else {
      userDisplay = userName;
    }

    switch (eventType) {
      case "recipe_view":
        return `Recipe ${recipeName} viewed by ${userDisplay}`;
      case "cta_click":
        return `Recipe ${recipeName} CTA clicked by ${userDisplay}`;
      case "contact_form_submit":
        return `Contact form submitted by ${userDisplay} for ${recipeName}`;
      default:
        return `Recipe ${recipeName} activity by ${userDisplay}`;
    }
  }

  /**
   * Helper method to aggregate CTA data for frontend table display
   */
  private aggregateCTADataForTable(rawData: any[]): any[] {
    const aggregated = new Map();

    rawData.forEach((item: any) => {
      const recipeName =
        item.recipe_name_meta || item.recipe_title || "Unknown Recipe";
      const ctaType = item.cta_type || "Contact Info";
      const key = `${recipeName}-${ctaType}`;

      if (aggregated.has(key)) {
        const existing = aggregated.get(key);
        existing.clicks += 1;
        // Update last clicked if this one is more recent
        if (new Date(item.created_at) > new Date(existing.last_clicked_at)) {
          existing.last_clicked_at = item.created_at;
        }
      } else {
        aggregated.set(key, {
          recipe_name: recipeName,
          recipe_id: item.recipe_id,
          cta_type: ctaType,
          clicks: 1,
          last_clicked_at: item.created_at,
        });
      }
    });

    return Array.from(aggregated.values()).sort(
      (a, b) =>
        new Date(b.last_clicked_at).getTime() -
        new Date(a.last_clicked_at).getTime()
    );
  }

  /**
   * Helper method to parse date ranges
   */
  private async getDateRange(
    dateRange: string,
    organizationId?: string | null
  ): Promise<{ startDate: Date; endDate: Date }> {
    const endDate = new Date();
    const startDate = new Date();

    // Get financial month setting for financial year calculations
    let financialStartMonth = 1; // Default to January (1-based)

    if (
      organizationId &&
      (dateRange === "last_year" || dateRange === "current_year")
    ) {
      try {
        // Get financial_month setting using the settings service
        const settingValue = await settingsService.getSetting(
          "financial_month",
          organizationId
        );

        if (settingValue) {
          // Parse financial month setting - support both numeric and string formats
          if (typeof settingValue === "string") {
            // Handle string formats like 'march-april', 'april', '4', etc.
            const monthMap: { [key: string]: number } = {
              january: 1,
              february: 2,
              march: 3,
              april: 4,
              may: 5,
              june: 6,
              july: 7,
              august: 8,
              september: 9,
              october: 10,
              november: 11,
              december: 12,
            };

            const lowerValue = settingValue.toLowerCase();

            // Handle formats like "april - march" where the first month is the start of financial year
            if (lowerValue.includes(" - ") || lowerValue.includes("-")) {
              // Split by dash and take the first month as the financial year start
              const parts = lowerValue.split(/\s*-\s*/);
              if (parts.length >= 2) {
                const firstMonth = parts[0].trim();
                for (const [monthName, monthNum] of Object.entries(monthMap)) {
                  if (
                    firstMonth === monthName ||
                    firstMonth.includes(monthName)
                  ) {
                    financialStartMonth = monthNum;
                    break;
                  }
                }
              }
            } else {
              // Single month name or other format
              for (const [monthName, monthNum] of Object.entries(monthMap)) {
                if (lowerValue.includes(monthName)) {
                  financialStartMonth = monthNum;
                  break;
                }
              }
            }

            // If no month name found, try to parse as number
            if (financialStartMonth === 1) {
              const numericValue = parseInt(settingValue);
              if (
                !isNaN(numericValue) &&
                numericValue >= 1 &&
                numericValue <= 12
              ) {
                financialStartMonth = numericValue;
              }
            }
          } else if (typeof settingValue === "number") {
            if (settingValue >= 1 && settingValue <= 12) {
              financialStartMonth = settingValue;
            }
          }
        }
      } catch (error) {
        // If there's an error fetching the setting, use default (January)
        console.warn(
          "Error fetching financial_month setting, using default:",
          error
        );
      }
    }

    switch (dateRange) {
      case "today":
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);
        break;
      case "this_week": {
        const dayOfWeek = endDate.getDay();
        startDate.setDate(endDate.getDate() - dayOfWeek);
        startDate.setHours(0, 0, 0, 0);
        break;
      }
      case "this_month":
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);
        break;
      case "last_7_days":
        startDate.setDate(endDate.getDate() - 7);
        break;
      case "last_month":
        startDate.setMonth(endDate.getMonth() - 1);
        break;
      case "last_30_days":
        startDate.setDate(endDate.getDate() - 30);
        break;
      case "last_90_days":
        startDate.setDate(endDate.getDate() - 90);
        break;
      case "last_year": {
        // Calculate last financial year based on financial_month setting
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-based

        // Determine which financial year we're currently in
        let currentFinancialYear: number;
        if (currentMonth >= financialStartMonth) {
          // We're in the financial year that started this calendar year
          currentFinancialYear = currentYear;
        } else {
          // We're in the financial year that started last calendar year
          currentFinancialYear = currentYear - 1;
        }

        // Last financial year is the one before current financial year
        const lastFinancialYear = currentFinancialYear - 1;

        // Set start date to beginning of last financial year (use UTC to avoid timezone issues)
        startDate.setUTCFullYear(lastFinancialYear, financialStartMonth - 1, 1); // Month is 0-based in setUTCFullYear
        startDate.setUTCHours(0, 0, 0, 0);

        // Set end date to end of last financial year (day before current financial year starts)
        endDate.setUTCFullYear(
          currentFinancialYear,
          financialStartMonth - 1,
          0
        ); // Day 0 = last day of previous month
        endDate.setUTCHours(23, 59, 59, 999);
        break;
      }
      case "current_year": {
        // Calculate current financial year based on financial_month setting
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1; // JavaScript months are 0-based

        // Determine which financial year we're currently in
        let currentFinancialYear: number;
        if (currentMonth >= financialStartMonth) {
          // We're in the financial year that started this calendar year
          currentFinancialYear = currentYear;
        } else {
          // We're in the financial year that started last calendar year
          currentFinancialYear = currentYear - 1;
        }

        // Set start date to beginning of current financial year (use UTC to avoid timezone issues)
        startDate.setUTCFullYear(
          currentFinancialYear,
          financialStartMonth - 1,
          1
        ); // Month is 0-based in setUTCFullYear
        startDate.setUTCHours(0, 0, 0, 0);

        // Set end date to end of current financial year
        // Next financial year starts one year later
        const nextFinancialYear = currentFinancialYear + 1;
        endDate.setUTCFullYear(nextFinancialYear, financialStartMonth - 1, 0); // Day 0 = last day of previous month
        endDate.setUTCHours(23, 59, 59, 999);
        break;
      }
      case "all_time":
        // Set to a very early date for all-time queries
        startDate.setFullYear(2020, 0, 1); // January 1, 2020
        break;
      default:
        startDate.setDate(endDate.getDate() - 30);
    }
    return { startDate, endDate };
  }

  // ============================================================================
  // ANALYTICS CONTROLLER METHODS - Organization-filtered and dynamic
  // ============================================================================

  /**
   * Get raw CTA click analytics data for export (not aggregated)
   */
  async getCtaClickAnalyticsRaw(
    organizationId: string | null | undefined,
    dateRange: string = "last_30_days",
    startDate?: string,
    endDate?: string,
    ctaType?: string,
    search?: string
  ): Promise<any> {
    try {
      const { startDate: defaultStart, endDate: defaultEnd } =
        await this.getDateRange(dateRange, organizationId);
      const finalStartDate = startDate ? new Date(startDate) : defaultStart;
      const finalEndDate = endDate ? new Date(endDate) : defaultEnd;

      // Build filters
      let orgFilter = "";
      if (organizationId) {
        orgFilter =
          "AND (a.organization_id = :organizationId OR a.organization_id IS NULL)";
      }

      let ctaFilter = "";
      if (ctaType) {
        ctaFilter =
          "AND JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.cta_type')) = :ctaType";
      }

      let searchFilter = "";
      if (search) {
        searchFilter = `AND (
          r.recipe_title LIKE :search OR
          JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.recipe_name')) LIKE :search OR
          JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.cta_type')) LIKE :search
        )`;
      }

      // Get raw data for export
      const dataQuery = `SELECT
           a.id,
           a.created_at,
           JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.cta_type')) as cta_type,
           JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.cta_text')) as cta_text,
           JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.recipe_name')) as recipe_name_meta,
           JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.tracking_source')) as tracking_source,
           r.recipe_title,
           r.id as recipe_id,
           a.user_id,
           a.ip_address
         FROM mo_recipe_analytics a
         LEFT JOIN mo_recipe r ON a.entity_id = r.id
         WHERE a.event_type = 'cta_click'
         AND a.created_at BETWEEN :startDate AND :endDate
         ${orgFilter}
         ${ctaFilter}
         ${searchFilter}
         ORDER BY a.created_at DESC`;

      const replacements: any = {
        startDate: finalStartDate,
        endDate: finalEndDate,
      };

      if (organizationId) {
        replacements.organizationId = organizationId;
      }
      if (ctaType) {
        replacements.ctaType = ctaType;
      }
      if (search) {
        replacements.search = `%${search}%`;
      }

      const dataResult = await sequelize.query(dataQuery, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return {
        status: true,
        data: dataResult,
        meta: {
          date_range: dateRange,
          start_date: finalStartDate.toISOString(),
          end_date: finalEndDate.toISOString(),
        },
      };
    } catch (error) {
      console.error("CTA Analytics Raw Error:", error);
      return {
        status: false,
        data: [],
        meta: {},
      };
    }
  }

  /**
   * Get CTA click analytics - Enhanced with search, cta_type filter, and date ranges
   */
  async getCtaClickAnalytics(
    organizationId: string | null | undefined,
    dateRange: string = "last_30_days",
    startDate?: string,
    endDate?: string,
    page?: number,
    limit?: number,
    ctaType?: string,
    search?: string, // General search term for recipe name, user info, etc.
    sort_order: string = "created_at:desc",
    sort_by?: string
  ): Promise<any> {
    try {
      const { startDate: defaultStart, endDate: defaultEnd } =
        await this.getDateRange(dateRange, organizationId);
      const finalStartDate = startDate ? new Date(startDate) : defaultStart;
      const finalEndDate = endDate ? new Date(endDate) : defaultEnd;

      // Organization filter
      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      // CTA Type filter - MySQL compatible
      const ctaFilter = ctaType
        ? `AND JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.cta_type')) = :ctaType`
        : "";

      // Enhanced search filter - MySQL compatible with correct metadata keys
      let searchFilter = "";
      if (search) {
        searchFilter = `AND (
          r.recipe_title LIKE :search
          OR JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.recipe_name')) LIKE :search
          OR JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.cta_type')) LIKE :search
          OR JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.cta_text')) LIKE :search
        )`;
      }

      // Handle sort parameters - only canonical field names allowed
      let finalSortField = "created_at";
      let finalSortOrder = "DESC";

      if (sort_by) {
        // Use sort_by parameter if provided (only canonical names)
        const validSortFields = [
          "created_at",
          "recipe_title",
          "cta_type",
          "cta_text",
        ];

        if (validSortFields.includes(sort_by)) {
          finalSortField = sort_by;
        }
      } else if (sort_order && sort_order.includes(":")) {
        // Handle combined format "field:order" (legacy support)
        const [sortField, sortOrder] = sort_order.split(":");
        const validSortFields = [
          "created_at",
          "recipe_title",
          "cta_type",
          "cta_text",
        ];

        if (validSortFields.includes(sortField)) {
          finalSortField = sortField;
        }
        finalSortOrder = sortOrder === "asc" ? "ASC" : "DESC";
      }

      // Handle sort_order parameter (asc/desc only)
      if (sort_order && !sort_order.includes(":")) {
        finalSortOrder = sort_order.toLowerCase() === "asc" ? "ASC" : "DESC";
      }

      // Base query for data
      let dataQuery = `SELECT
           a.id,
           a.created_at,
           JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.cta_type')) as cta_type,
           JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.cta_text')) as cta_text,
           JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.recipe_name')) as recipe_name_meta,
           JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.tracking_source')) as tracking_source,
           r.recipe_title,
           r.id as recipe_id,
           a.user_id,
           a.ip_address
         FROM mo_recipe_analytics a
         LEFT JOIN mo_recipe r ON a.entity_id = r.id
         WHERE a.event_type = 'cta_click'
         AND a.created_at BETWEEN :startDate AND :endDate
         ${orgFilter}
         ${ctaFilter}
         ${searchFilter}
         ORDER BY ${finalSortField === "cta_type" ? "JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.cta_type'))" : finalSortField === "recipe_title" ? "r.recipe_title" : finalSortField === "cta_text" ? "JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.cta_text'))" : "a." + finalSortField} ${finalSortOrder}`;

      const baseReplacements: any = {
        startDate: finalStartDate,
        endDate: finalEndDate,
        organizationId,
        ctaType,
        search: search ? `%${search}%` : undefined,
      };

      // Handle pagination
      if (page && limit) {
        const offset = (page - 1) * limit;

        // Count query for pagination
        const countResult = await sequelize.query(
          `SELECT COUNT(*) as total
           FROM mo_recipe_analytics a
           LEFT JOIN mo_recipe r ON a.entity_id = r.id
           WHERE a.event_type = 'cta_click'
           AND a.created_at BETWEEN :startDate AND :endDate
           ${orgFilter}
           ${ctaFilter}
           ${searchFilter}`,
          {
            replacements: baseReplacements,
            type: QueryTypes.SELECT,
          }
        );

        // Add pagination to data query
        dataQuery += ` LIMIT :limit OFFSET :offset`;
        baseReplacements.limit = limit;
        baseReplacements.offset = offset;

        const dataResult = await sequelize.query(dataQuery, {
          replacements: baseReplacements,
          type: QueryTypes.SELECT,
        });

        const total = (countResult[0] as any)?.total || 0;
        const totalPages = Math.ceil(total / limit);

        // For frontend table display - aggregate by recipe and CTA type
        const aggregatedData = this.aggregateCTADataForTable(dataResult);

        // Clean data - ensure consistent structure with only important fields
        const cleanedData = aggregatedData.map((item: any) => {
          const cleaned: any = {
            recipe_name: item.recipe_name,
            recipe_id: item.recipe_id,
            cta_type: item.cta_type,
            clicks: item.clicks,
            last_clicked_at: item.last_clicked_at,
          };

          return cleaned;
        });

        // Clean meta - remove null fields
        const cleanMeta: any = {
          date_range: dateRange,
        };

        // Only add date fields if they have meaningful values
        if (startDate && endDate) {
          cleanMeta.start_date = finalStartDate.toISOString();
          cleanMeta.end_date = finalEndDate.toISOString();
        }

        return {
          status: true,
          data: cleanedData,
          meta: cleanMeta,
          pagination: {
            current_page: page,
            page_size: limit,
            total_records: total,
            total_pages: totalPages,
            has_next: page < totalPages,
            has_prev: page > 1,
          },
        };
      } else {
        // No pagination - return all results
        const dataResult = await sequelize.query(dataQuery, {
          replacements: baseReplacements,
          type: QueryTypes.SELECT,
        });

        // For frontend table display - aggregate by recipe and CTA type
        const aggregatedData = this.aggregateCTADataForTable(dataResult);

        // Clean data - ensure consistent structure with only important fields
        const cleanedData = aggregatedData.map((item: any) => {
          const cleaned: any = {
            recipe_name: item.recipe_name,
            recipe_id: item.recipe_id,
            cta_type: item.cta_type,
            clicks: item.clicks,
            last_clicked_at: item.last_clicked_at,
          };

          return cleaned;
        });

        // Clean meta - remove null fields
        const cleanMeta: any = {
          date_range: dateRange,
        };

        // Only add date fields if they have meaningful values
        if (startDate && endDate) {
          cleanMeta.start_date = finalStartDate.toISOString();
          cleanMeta.end_date = finalEndDate.toISOString();
        }

        return {
          status: true,
          data: cleanedData,
          meta: cleanMeta,
          pagination: {
            current_page: 1,
            page_size: cleanedData.length,
            total_records: cleanedData.length,
            total_pages: 1,
            has_next: false,
            has_prev: false,
          },
        };
      }
    } catch (error) {
      console.error("CTA Analytics Error:", error);
      return {
        status: false,
        data: [],
        pagination: {
          current_page: page || 1,
          page_size: limit || 50,
          total_records: 0,
          total_pages: 0,
          has_next: false,
          has_prev: false,
        },
      };
    }
  }

  /**
   * Get contact submission analytics - Enhanced with recipe name search, user email search, and date ranges
   */
  async getContactSubmissionAnalytics(
    organizationId: string | null | undefined,
    dateRange: string = "last_30_days",
    startDate?: string,
    endDate?: string,
    page?: number,
    limit?: number,
    search?: string, // General search for recipe name, user email, user name
    sort_order: string = "created_at:desc",
    sort_by?: string
  ): Promise<any> {
    try {
      const { startDate: defaultStart, endDate: defaultEnd } =
        await this.getDateRange(dateRange, organizationId);
      const finalStartDate = startDate ? new Date(startDate) : defaultStart;
      const finalEndDate = endDate ? new Date(endDate) : defaultEnd;

      // Organization filter
      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";

      // Enhanced search filter - MySQL compatible JSON syntax with correct field names
      let searchFilter = "";
      if (search) {
        searchFilter = `AND (
          r.recipe_title LIKE :search
          OR JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.contact_email')) LIKE :search
          OR JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.contact_name')) LIKE :search
          OR JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.message')) LIKE :search
          OR JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.recipe_name')) LIKE :search
        )`;
      }

      // Handle sort parameters - only canonical field names allowed
      let finalSortField = "created_at";
      let finalSortOrder = "DESC";

      if (sort_by) {
        // Use sort_by parameter if provided (only canonical names)
        const validSortFields = [
          "created_at",
          "recipe_title",
          "contact_name",
          "contact_email",
        ];

        if (validSortFields.includes(sort_by)) {
          finalSortField = sort_by;
        }
      } else if (sort_order && sort_order.includes(":")) {
        // Handle combined format "field:order" (legacy support)
        const [sortField, sortOrder] = sort_order.split(":");
        const validSortFields = [
          "created_at",
          "recipe_title",
          "contact_name",
          "contact_email",
        ];

        if (validSortFields.includes(sortField)) {
          finalSortField = sortField;
        }
        finalSortOrder = sortOrder === "asc" ? "ASC" : "DESC";
      }

      // Handle sort_order parameter (asc/desc only)
      if (sort_order && !sort_order.includes(":")) {
        finalSortOrder = sort_order.toLowerCase() === "asc" ? "ASC" : "DESC";
      }

      // Base query with MySQL compatible JSON syntax - Fixed field names
      let query = `
        SELECT
          a.id,
          a.created_at,
          COALESCE(JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.contact_email')), '') as contact_email,
          COALESCE(JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.contact_name')), '') as contact_name,
          COALESCE(JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.message')), '') as message,
          COALESCE(JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.contact_mobile')), '') as contact_mobile,
          COALESCE(JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.recipe_name')), '') as recipe_name_meta,
          r.recipe_title,
          r.id as recipe_id,
          a.user_id,
          a.ip_address,
          a.metadata
        FROM mo_recipe_analytics a
        LEFT JOIN mo_recipe r ON a.entity_id = r.id
        WHERE a.event_type = 'contact_form_submit'
        AND a.created_at BETWEEN :startDate AND :endDate
        ${orgFilter}
        ${searchFilter}
        ORDER BY ${finalSortField === "recipe_title" ? "r.recipe_title" : finalSortField === "contact_email" ? "COALESCE(JSON_UNQUOTE(JSON_EXTRACT(a.metadata, '$.contact_email')), '')" : "a." + finalSortField} ${finalSortOrder}
      `;

      const replacements: any = {
        startDate: finalStartDate,
        endDate: finalEndDate,
        organizationId,
        search: search ? `%${search}%` : undefined,
      };

      // Add pagination if provided
      if (page && limit) {
        const offset = (page - 1) * limit;
        query += ` LIMIT :limit OFFSET :offset`;
        replacements.limit = limit;
        replacements.offset = offset;

        // Get total count for pagination
        const countResult = await sequelize.query(
          `SELECT COUNT(*) as total
           FROM mo_recipe_analytics a
           LEFT JOIN mo_recipe r ON a.entity_id = r.id
           WHERE a.event_type = 'contact_form_submit'
           AND a.created_at BETWEEN :startDate AND :endDate
           ${orgFilter}
           ${searchFilter}`,
          {
            replacements: {
              startDate: finalStartDate,
              endDate: finalEndDate,
              organizationId,
              search: search ? `%${search}%` : undefined,
            },
            type: QueryTypes.SELECT,
          }
        );

        const dataResult = await sequelize.query(query, {
          replacements,
          type: QueryTypes.SELECT,
        });

        const total = (countResult[0] as any)?.total || 0;
        const totalPages = Math.ceil(total / limit);

        // Clean data - ensure consistent structure for frontend table
        const cleanedData = dataResult.map((item: any) => {
          // Fallback to parsing metadata if JSON extraction failed
          let parsedMetadata: any = {};
          if (item.metadata && typeof item.metadata === "string") {
            try {
              parsedMetadata = JSON.parse(item.metadata);
            } catch {
              // Silently handle JSON parse errors
            }
          } else if (item.metadata && typeof item.metadata === "object") {
            parsedMetadata = item.metadata;
          }

          const cleaned: any = {
            id: item.id,
            recipe_name:
              item.recipe_name_meta ||
              parsedMetadata.recipe_name ||
              item.recipe_title ||
              "Unknown Recipe",
            recipe_id: item.recipe_id || null,
            name: item.contact_name || parsedMetadata.contact_name || "Unknown",
            email:
              item.contact_email || parsedMetadata.contact_email || "No Email",
            mobile:
              item.contact_mobile ||
              parsedMetadata.contact_mobile ||
              "No Mobile",
            message: item.message || parsedMetadata.message || "No Message",
            submitted_on: item.created_at,
          };

          return cleaned;
        });

        // Clean meta - remove null fields
        const cleanMeta: any = {
          date_range: dateRange,
        };

        // Only add date fields if they have meaningful values
        if (startDate && endDate) {
          cleanMeta.start_date = finalStartDate.toISOString();
          cleanMeta.end_date = finalEndDate.toISOString();
        }

        return {
          status: true,
          data: cleanedData,
          meta: cleanMeta,
          pagination: {
            current_page: page,
            page_size: limit,
            total_records: total,
            total_pages: totalPages,
            has_next: page < totalPages,
            has_prev: page > 1,
          },
        };
      } else {
        // No pagination - return all results
        const dataResult = await sequelize.query(query, {
          replacements,
          type: QueryTypes.SELECT,
        });

        // Clean data - ensure consistent structure for frontend table
        const cleanedData = dataResult.map((item: any) => {
          // Fallback to parsing metadata if JSON extraction failed
          let parsedMetadata: any = {};
          if (item.metadata && typeof item.metadata === "string") {
            try {
              parsedMetadata = JSON.parse(item.metadata);
            } catch {
              // Silently handle JSON parse errors
            }
          } else if (item.metadata && typeof item.metadata === "object") {
            parsedMetadata = item.metadata;
          }

          const cleaned: any = {
            id: item.id,
            recipe_name:
              item.recipe_name_meta ||
              parsedMetadata.recipe_name ||
              item.recipe_title ||
              "Unknown Recipe",
            recipe_id: item.recipe_id || null,
            name: item.contact_name || parsedMetadata.contact_name || "Unknown",
            email:
              item.contact_email || parsedMetadata.contact_email || "No Email",
            mobile:
              item.contact_mobile ||
              parsedMetadata.contact_mobile ||
              "No Mobile",
            message: item.message || parsedMetadata.message || "No Message",
            submitted_on: item.created_at,
          };

          return cleaned;
        });

        // Clean meta - remove null fields
        const cleanMeta: any = {
          date_range: dateRange,
        };

        // Only add date fields if they have meaningful values
        if (startDate && endDate) {
          cleanMeta.start_date = finalStartDate.toISOString();
          cleanMeta.end_date = finalEndDate.toISOString();
        }

        return {
          status: true,
          data: cleanedData,
          meta: cleanMeta,
          total: cleanedData.length,
        };
      }
    } catch (error) {
      console.error("Contact Analytics Error:", error);
      return {
        status: false,
        data: [],
        total: 0,
      };
    }
  }

  /**
   * Get recipe view analytics - Organization filtered with pagination
   */
  async getRecipeViewAnalytics(
    organizationId: string | null | undefined,
    dateRange: string = "last_30_days",
    startDate?: string,
    endDate?: string,
    page?: number,
    limit?: number,
    recipeName?: string,
    sort: string = "created_at:desc"
  ): Promise<any> {
    try {
      const { startDate: defaultStart, endDate: defaultEnd } =
        await this.getDateRange(dateRange, organizationId);
      const finalStartDate = startDate ? new Date(startDate) : defaultStart;
      const finalEndDate = endDate ? new Date(endDate) : defaultEnd;

      const orgFilter = organizationId
        ? `AND a.organization_id = :organizationId`
        : "";
      const recipeFilter = recipeName
        ? `AND r.recipe_title ILIKE :recipeName`
        : "";

      // Parse sort parameter
      const [sortField, sortOrder] = sort.split(":");
      const validSortFields = ["created_at", "recipe_title", "view_count"];
      const finalSortField = validSortFields.includes(sortField)
        ? sortField
        : "created_at";
      const finalSortOrder = sortOrder === "asc" ? "ASC" : "DESC";

      let query = `
        SELECT
          r.id as recipe_id,
          r.recipe_title,
          COUNT(a.id) as view_count,
          MAX(a.created_at) as last_viewed
        FROM mo_recipe_analytics a
        LEFT JOIN mo_recipe r ON a.entity_id = r.id
        WHERE a.event_type = 'recipe_view'
        AND a.created_at BETWEEN :startDate AND :endDate
        ${orgFilter}
        ${recipeFilter}
        GROUP BY r.id, r.recipe_title
        ORDER BY ${finalSortField === "recipe_title" ? "r.recipe_title" : finalSortField === "view_count" ? "COUNT(a.id)" : "MAX(a.created_at)"} ${finalSortOrder}
      `;

      const replacements: any = {
        startDate: finalStartDate,
        endDate: finalEndDate,
        organizationId,
        recipeName: recipeName ? `%${recipeName}%` : undefined,
      };

      // Add pagination if provided
      if (page && limit) {
        const offset = (page - 1) * limit;
        query += ` LIMIT :limit OFFSET :offset`;
        replacements.limit = limit;
        replacements.offset = offset;
      }

      const dataResult = await sequelize.query(query, {
        replacements,
        type: QueryTypes.SELECT,
      });

      return {
        status: true,
        data: dataResult,
        total: dataResult.length,
      };
    } catch {
      return {
        status: false,
        data: [],
        total: 0,
      };
    }
  }

  /**
   * Get analytics summary - Organization filtered
   */
  async getAnalyticsSummary(options: {
    organizationId: string | null | undefined;
    page?: number;
    limit?: number;
    dateRange?: string;
    event_type?: string;
    entity_type?: string;
    entity_id?: number;
    start_date?: string;
    end_date?: string;
  }): Promise<any> {
    try {
      const { organizationId, dateRange = "last_30_days" } = options;
      const { startDate, endDate } = await this.getDateRange(
        dateRange,
        organizationId
      );

      const orgFilter = organizationId
        ? `AND organization_id = :organizationId`
        : "";

      // Get summary statistics
      const summaryResult = await sequelize.query(
        `SELECT
           COUNT(CASE WHEN event_type = 'recipe_view' THEN 1 END) as total_views,
           COUNT(CASE WHEN event_type = 'cta_click' THEN 1 END) as total_cta_clicks,
           COUNT(CASE WHEN event_type = 'contact_form_submit' THEN 1 END) as total_contacts,
           COUNT(DISTINCT entity_id) as unique_recipes,
           COUNT(DISTINCT user_id) as unique_users
         FROM mo_recipe_analytics
         WHERE created_at BETWEEN :startDate AND :endDate
         ${orgFilter}`,
        {
          replacements: { startDate, endDate, organizationId },
          type: QueryTypes.SELECT,
        }
      );

      return {
        status: true,
        data: summaryResult[0] || {
          total_views: 0,
          total_cta_clicks: 0,
          total_contacts: 0,
          unique_recipes: 0,
          unique_users: 0,
        },
      };
    } catch {
      return {
        status: false,
        data: {
          total_views: 0,
          total_cta_clicks: 0,
          total_contacts: 0,
          unique_recipes: 0,
          unique_users: 0,
        },
      };
    }
  }

  /**
   * Get recipe view statistics for specific recipe - Organization filtered
   */
  async getRecipeViewStatistics(
    recipeId: number,
    organizationId: string | null | undefined
  ): Promise<any> {
    try {
      // First check if recipe exists and is private
      const recipeCheckQuery = `
        SELECT
          r.id,
          r.has_recipe_private_visibility,
          COUNT(ru.user_id) as assigned_users_count
        FROM mo_recipe r
        LEFT JOIN mo_recipe_user ru ON r.id = ru.recipe_id AND ru.status = 'active'
        WHERE r.id = :recipeId
          AND r.recipe_status != 'deleted'
          ${organizationId ? "AND r.organization_id = :organizationId" : ""}
        GROUP BY r.id, r.has_recipe_private_visibility
      `;

      const recipeCheck = await sequelize.query(recipeCheckQuery, {
        replacements: { recipeId, organizationId },
        type: QueryTypes.SELECT,
      });

      if (!recipeCheck.length) {
        return {
          status: false,
          message: "Recipe not found or access denied",
        };
      }

      const recipeData = recipeCheck[0] as any;

      // Check if recipe is private
      if (!recipeData.has_recipe_private_visibility) {
        return {
          status: false,
          message:
            "Recipe view statistics are only available for private recipes",
        };
      }

      // Check if recipe has assigned users
      if (parseInt(recipeData.assigned_users_count) === 0) {
        return {
          status: false,
          message: "Recipe has no assigned users",
        };
      }

      // Get all users (both assigned users and users who have viewed the recipe)
      const userAnalyticsQuery = `
         SELECT
           u.id as user_id,
           CONCAT(COALESCE(u.user_first_name, ''), ' ', COALESCE(u.user_last_name, '')) as user_full_name,
           u.user_email,
           IF((u.user_avatar IS NOT NULL AND u.user_avatar != ''), CONCAT('${global.config.API_UPLOAD_URL}', (SELECT item_location FROM nv_items WHERE id = u.user_avatar)), '') AS user_avatar_link,
           COALESCE(b.branch_name, '') as user_branch,
           COALESCE(d.department_name, '') as user_department,
           MAX(ra.created_at) as last_recipe_view,
           COUNT(ra.id) as total_view_count,
           CASE WHEN ru.user_id IS NOT NULL THEN 1 ELSE 0 END as is_assigned
         FROM (
           -- Get all assigned users
           SELECT DISTINCT ru.user_id
           FROM mo_recipe_user ru
           WHERE ru.recipe_id = ?
             AND ru.status = 'active'
           
           UNION
           
           -- Get all users who have viewed the recipe
           SELECT DISTINCT ra.user_id
           FROM mo_recipe_analytics ra
           WHERE ra.entity_id = ?
             AND ra.entity_type = 'recipe'
             AND ra.event_type = 'recipe_view'
             AND ra.user_id IS NOT NULL
         ) all_users
         INNER JOIN nv_users u ON all_users.user_id = u.id AND u.user_status NOT IN ('cancelled', 'deleted')
         LEFT JOIN nv_branches b ON u.branch_id = b.id
         LEFT JOIN nv_departments d ON u.department_id = d.id
         LEFT JOIN mo_recipe_user ru ON ru.recipe_id = ? AND ru.user_id = u.id AND ru.status = 'active'
         LEFT JOIN mo_recipe_analytics ra ON ra.entity_id = ?
           AND ra.entity_type = 'recipe'
           AND ra.event_type = 'recipe_view'
           AND ra.user_id = u.id
         GROUP BY u.id, u.user_first_name, u.user_last_name, u.user_email, u.user_avatar, 
                  b.branch_name, d.department_name, ru.user_id
         ORDER BY total_view_count DESC, user_full_name ASC
       `;

      const result = await sequelize.query(userAnalyticsQuery, {
        replacements: [recipeId, recipeId, recipeId, recipeId],
        type: QueryTypes.SELECT,
      });

      // Format response with optimized data structure
      const userData = result.map((row: any) => ({
        id: row.user_id,
        user_full_name: row.user_full_name.trim() || "Unknown User",
        user_email: row.user_email || "",
        user_avatar: row.user_avatar_link || "",
        user_branch: row.user_branch || "",
        user_department: row.user_department || "",
        last_recipe_view: row.last_recipe_view || null,
        total_view_count: parseInt(row.total_view_count) || 0,
        is_assigned: parseInt(row.is_assigned) === 1,
      }));

      return {
        status: true,
        message: "Recipe view statistics retrieved successfully",
        data: userData,
        summary: {
          total_users: userData.length,
          assigned_users: userData.filter((u: any) => u.is_assigned).length,
          viewers_only: userData.filter((u: any) => !u.is_assigned).length,
        },
      };
    } catch {
      return {
        status: false,
        data: {
          id: recipeId,
          recipe_title: null,
          total_views: 0,
          unique_viewers: 0,
          last_viewed: null,
          first_viewed: null,
        },
      };
    }
  }

  /**
   * Reset recipe view statistics for specific recipe - Organization filtered
   */
  async resetRecipeViewStatistics(
    recipeId: number,
    organizationId: string | null | undefined,
    userIds?: number[] | string
  ): Promise<any> {
    try {
      // First, verify the recipe exists and is private with assigned users
      const recipeCheckQuery = `
        SELECT
          r.id,
          r.recipe_title,
          r.has_recipe_private_visibility,
          r.organization_id,
          COUNT(ru.user_id) as assigned_users_count
        FROM mo_recipe r
        LEFT JOIN mo_recipe_user ru ON r.id = ru.recipe_id
        WHERE r.id = ?
          AND r.recipe_status != ?
          ${organizationId ? "AND r.organization_id = ?" : ""}
        GROUP BY r.id, r.recipe_title, r.has_recipe_private_visibility, r.organization_id
      `;

      const replacements: (number | string)[] = [recipeId, "deleted"];
      if (organizationId) {
        replacements.push(organizationId);
      }

      const recipeResult = await sequelize.query(recipeCheckQuery, {
        replacements,
        type: QueryTypes.SELECT,
      });

      if (!recipeResult.length) {
        return {
          status: false,
          message: "Recipe not found or access denied",
        };
      }

      const recipe = recipeResult[0] as any;

      // Check if recipe is private
      if (!recipe.has_recipe_private_visibility) {
        return {
          status: false,
          message:
            "Recipe view statistics reset is only available for private recipes",
        };
      }

      // Check if recipe has assigned users
      const assignedUsersCount = parseInt(recipe.assigned_users_count) || 0;
      if (assignedUsersCount === 0) {
        return {
          status: false,
          message: "Recipe must have assigned users to reset statistics",
        };
      }

      let deletedCount = 0;
      let resetMessage = "";

      // Handle different formats for "all" users
      const isResetAll =
        userIds === "all" ||
        (Array.isArray(userIds) &&
          userIds.length === 1 &&
          typeof userIds[0] === "string" &&
          userIds[0] === "all");
      if (isResetAll) {
        // Reset for all users - remove all statistics for this recipe
        const destroyObj: any = {
          event_type: "recipe_view",
          entity_type: "recipe",
          entity_id: recipeId,
        };

        if (organizationId) {
          destroyObj.organization_id = organizationId;
        }

        deletedCount = await Analytics.destroy({
          where: destroyObj,
        });

        resetMessage = `Recipe view statistics reset successfully for all users (${deletedCount} records removed)`;
      } else if (
        userIds &&
        Array.isArray(userIds) &&
        userIds.length > 0 &&
        !isResetAll
      ) {
        // Reset for specific user IDs - only remove statistics for those users
        const destroyObj: any = {
          event_type: "recipe_view",
          entity_type: "recipe",
          entity_id: recipeId,
          user_id: userIds,
        };

        if (organizationId) {
          destroyObj.organization_id = organizationId;
        }

        deletedCount = await Analytics.destroy({
          where: destroyObj,
        });

        resetMessage = `Recipe view statistics reset successfully for ${userIds.length} specified users (${deletedCount} records removed)`;
      } else {
        return {
          status: false,
          message:
            "No users specified for statistics reset. Use user_ids array or 'all' to reset statistics.",
        };
      }

      return {
        status: true,
        message: resetMessage,
        data: {
          recipe_id: recipeId,
          recipe_title: recipe.recipe_title,
          records_deleted: deletedCount,
          reset_type: isResetAll ? "all_users" : "specific_users",
          affected_users: isResetAll
            ? assignedUsersCount
            : Array.isArray(userIds)
              ? userIds.length
              : 0,
        },
      };
    } catch (error: any) {
      // Provide more specific error messages
      let errorMessage = "Error resetting recipe view statistics";

      if (error.name === "SequelizeConnectionError") {
        errorMessage = "Database connection error";
      } else if (error.name === "SequelizeDatabaseError") {
        errorMessage = "Database query error";
      } else if (error.message) {
        errorMessage = `Error: ${error.message}`;
      }

      return {
        status: false,
        message: errorMessage,
      };
    }
  }
}

export default new AnalyticsService();
